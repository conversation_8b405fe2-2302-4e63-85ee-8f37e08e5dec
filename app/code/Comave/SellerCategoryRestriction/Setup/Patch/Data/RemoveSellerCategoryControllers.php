<?php

declare(strict_types=1);

namespace Comave\SellerCategoryRestriction\Setup\Patch\Data;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class RemoveSellerCategoryControllers implements DataPatchInterface
{
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup
    ) {}

    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();

        // Remove seller category controller entries from marketplace_controller_list
        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('marketplace_controller_list');

        $connection->delete(
            $tableName,
            [
                'controller_path LIKE ?' => 'mpsellercategory/category/%'
            ]
        );

        $this->moduleDataSetup->endSetup();
    }
}
